<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>热力图颜色方案预览</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .color-bar {
            height: 60px;
            margin: 20px 0;
            border-radius: 4px;
            position: relative;
            background: linear-gradient(to right, 
                rgb(70, 130, 180) 0%,
                rgb(145, 105, 130) 50%,
                rgb(220, 80, 80) 100%);
        }
        .labels {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }
        .comparison {
            display: flex;
            gap: 20px;
            margin: 30px 0;
        }
        .old-scheme, .new-scheme {
            flex: 1;
            text-align: center;
        }
        .old-bar {
            height: 40px;
            background: linear-gradient(to right, 
                rgb(0, 0, 255) 0%,
                rgb(255, 0, 0) 100%);
            border-radius: 4px;
            margin: 10px 0;
        }
        .new-bar {
            height: 40px;
            background: linear-gradient(to right, 
                rgb(70, 130, 180) 0%,
                rgb(145, 105, 130) 50%,
                rgb(220, 80, 80) 100%);
            border-radius: 4px;
            margin: 10px 0;
        }
        .opacity-demo {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            align-items: center;
        }
        .opacity-box {
            width: 60px;
            height: 40px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
        }
        h1, h2 {
            color: #333;
        }
        .feature-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .feature-list li {
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>热力图颜色方案改进</h1>
        
        <h2>新颜色方案</h2>
        <div class="color-bar"></div>
        <div class="labels">
            <span>低密度 (天然蓝)</span>
            <span>中密度</span>
            <span>高密度 (温暖红)</span>
        </div>
        
        <h2>方案对比</h2>
        <div class="comparison">
            <div class="old-scheme">
                <h3>原方案</h3>
                <div class="old-bar"></div>
                <p>纯蓝 → 纯红</p>
                <p>固定透明度 50%</p>
            </div>
            <div class="new-scheme">
                <h3>新方案</h3>
                <div class="new-bar"></div>
                <p>天然蓝 → 温暖红</p>
                <p>动态透明度 30%-75%</p>
            </div>
        </div>
        
        <h2>动态透明度演示</h2>
        <div class="opacity-demo">
            <span>密度等级：</span>
            <div class="opacity-box" style="background-color: rgba(70, 130, 180, 0.3);">0.2</div>
            <div class="opacity-box" style="background-color: rgba(120, 115, 150, 0.45);">0.4</div>
            <div class="opacity-box" style="background-color: rgba(170, 100, 120, 0.6);">0.6</div>
            <div class="opacity-box" style="background-color: rgba(195, 90, 100, 0.68);">0.8</div>
            <div class="opacity-box" style="background-color: rgba(220, 80, 80, 0.75);">1.0</div>
        </div>
        
        <h2>改进特性</h2>
        <div class="feature-list">
            <ul>
                <li><strong>更柔和的颜色</strong>：使用天然蓝 (SteelBlue) 和温暖红 (IndianRed)，视觉更舒适</li>
                <li><strong>动态透明度</strong>：根据人员密度调整透明度 (30%-75%)，层次更分明</li>
                <li><strong>平滑过渡</strong>：颜色渐变更自然，避免突兀的颜色跳跃</li>
                <li><strong>保持直观性</strong>：仍然遵循蓝色=低密度，红色=高密度的直观认知</li>
                <li><strong>背景适应性</strong>：在白色建筑背景下有更好的对比度</li>
            </ul>
        </div>
        
        <h2>颜色值参考</h2>
        <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
            <tr style="background: #f8f9fa;">
                <th style="padding: 10px; border: 1px solid #ddd;">密度比例</th>
                <th style="padding: 10px; border: 1px solid #ddd;">RGB值</th>
                <th style="padding: 10px; border: 1px solid #ddd;">透明度</th>
                <th style="padding: 10px; border: 1px solid #ddd;">颜色预览</th>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">0% (无人员)</td>
                <td style="padding: 10px; border: 1px solid #ddd;">-</td>
                <td style="padding: 10px; border: 1px solid #ddd;">0% (完全透明)</td>
                <td style="padding: 10px; border: 1px solid #ddd; background: transparent;"></td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">25%</td>
                <td style="padding: 10px; border: 1px solid #ddd;">rgb(107, 118, 155)</td>
                <td style="padding: 10px; border: 1px solid #ddd;">41%</td>
                <td style="padding: 10px; border: 1px solid #ddd; background: rgba(107, 118, 155, 0.41);"></td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">50%</td>
                <td style="padding: 10px; border: 1px solid #ddd;">rgb(145, 105, 130)</td>
                <td style="padding: 10px; border: 1px solid #ddd;">53%</td>
                <td style="padding: 10px; border: 1px solid #ddd; background: rgba(145, 105, 130, 0.53);"></td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">75%</td>
                <td style="padding: 10px; border: 1px solid #ddd;">rgb(182, 93, 105)</td>
                <td style="padding: 10px; border: 1px solid #ddd;">64%</td>
                <td style="padding: 10px; border: 1px solid #ddd; background: rgba(182, 93, 105, 0.64);"></td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">100% (最高密度)</td>
                <td style="padding: 10px; border: 1px solid #ddd;">rgb(220, 80, 80)</td>
                <td style="padding: 10px; border: 1px solid #ddd;">75%</td>
                <td style="padding: 10px; border: 1px solid #ddd; background: rgba(220, 80, 80, 0.75);"></td>
            </tr>
        </table>
    </div>
</body>
</html>
